/**
 * Role Custom Admin JavaScript
 * Eğitmenler sayfası için çıkar butonu
 */

jQuery(document).ready(function($) {
    'use strict';

    // Çıkar butonlarına click event'i ekle
    $(document).on('click', '.role-custom-remove-btn', function(e) {
        e.preventDefault();

        const $btn = $(this);
        const userId = $btn.data('user-id');
        const userName = $btn.data('user-name');

        // Onay iste
        const confirmMessage = 'UYARI: Bu işlem geri alınamaz!\n\n' +
                              userName + ' kullanıcısı için:\n' +
                              '1. Tüm eğitmen meta verileri silinecek\n' +
                              '2. Kullanıcı abone rolüne çekilecek\n\n' +
                              'Devam etmek istediğinizden emin misiniz?';

        if (!confirm(confirmMessage)) {
            return;
        }

        // Buton durumunu güncelle
        const originalText = $btn.text();
        $btn.prop('disabled', true).text('İşlem yapılıyor...');

        // AJAX isteği
        $.ajax({
            url: roleCustomAjax.ajaxUrl,
            type: 'POST',
            data: {
                action: 'role_custom_remove_instructor',
                nonce: roleCustomAjax.nonce,
                user_id: userId
            },
            success: function(response) {
                if (response.success) {
                    let message = response.data.message;

                    // Detaylı debug bilgisi
                    if (response.data.total_found !== undefined) {
                        message += '\n\nİşlem Öncesi Bulunan Tutor Meta Veri: ' + response.data.total_found;
                    }

                    if (response.data.deleted_count !== undefined) {
                        message += '\nSilinen Meta Veri Sayısı: ' + response.data.deleted_count;
                    }

                    if (response.data.tutor_utils_used) {
                        message += '\n\n✓ Tutor Utils remove_instructor_role() kullanıldı';
                    }

                    if (response.data.meta_before && response.data.meta_before.length > 0) {
                        message += '\n\nİşlem Öncesi Meta Veriler:\n' + response.data.meta_before.join('\n');
                    }

                    if (response.data.deleted_keys && response.data.deleted_keys.length > 0) {
                        message += '\n\nSilme İşlemleri:\n' + response.data.deleted_keys.join('\n');
                    }

                    alert(message);

                    // Sayfayı yenile
                    location.reload();
                } else {
                    alert(response.data || roleCustomAjax.messages.error);
                }
            },
            error: function() {
                alert(roleCustomAjax.messages.error);
            },
            complete: function() {
                $btn.prop('disabled', false).text(originalText);
            }
        });
    });

    // Debug butonlarına click event'i ekle
    $(document).on('click', '.role-custom-debug-btn', function(e) {
        e.preventDefault();

        const $btn = $(this);
        const userId = $btn.data('user-id');
        const userName = $btn.data('user-name');

        // Buton durumunu güncelle
        const originalText = $btn.text();
        $btn.prop('disabled', true).text('Yükleniyor...');

        // AJAX isteği
        $.ajax({
            url: roleCustomAjax.ajaxUrl,
            type: 'POST',
            data: {
                action: 'role_custom_debug_user_meta',
                nonce: roleCustomAjax.nonce,
                user_id: userId
            },
            success: function(response) {
                if (response.success) {
                    let message = 'KULLANICI DEBUG BİLGİSİ\n';
                    message += '========================\n';
                    message += 'Kullanıcı: ' + response.data.user_name + '\n';
                    message += 'Roller: ' + response.data.user_roles.join(', ') + '\n';
                    message += 'Toplam Meta Veri: ' + response.data.total_meta + '\n';
                    message += 'Tutor Meta Veri: ' + response.data.tutor_meta_count + '\n';
                    message += 'Tutor Utils Mevcut: ' + (response.data.tutor_utils_available ? 'Evet' : 'Hayır') + '\n\n';

                    if (response.data.tutor_meta.length > 0) {
                        message += 'TUTOR META VERİLERİ:\n';
                        message += '===================\n';
                        response.data.tutor_meta.forEach(function(meta) {
                            message += '• ' + meta.key + ': ' + meta.value + '\n';
                        });
                    } else {
                        message += 'HİÇ TUTOR META VERİSİ BULUNAMADI!\n';
                        message += 'Bu kullanıcı zaten temizlenmiş olabilir.\n';
                    }

                    alert(message);
                } else {
                    alert(response.data || roleCustomAjax.messages.error);
                }
            },
            error: function() {
                alert(roleCustomAjax.messages.error);
            },
            complete: function() {
                $btn.prop('disabled', false).text(originalText);
            }
        });
    });

    // Meta Temizle butonlarına click event'i ekle
    $(document).on('click', '.role-custom-clean-meta-btn', function(e) {
        e.preventDefault();

        const $btn = $(this);
        const userId = $btn.data('user-id');
        const userName = $btn.data('user-name');

        // Onay iste
        const confirmMessage = userName + ' kullanıcısının sadece meta verilerini silmek istediğinizden emin misiniz?\n\n' +
                              'Bu işlem:\n' +
                              '• Eğitmen meta verilerini silecek\n' +
                              '• Kullanıcının rolünü değiştirmeyecek\n\n' +
                              'Devam edilsin mi?';

        if (!confirm(confirmMessage)) {
            return;
        }

        // Buton durumunu güncelle
        const originalText = $btn.text();
        $btn.prop('disabled', true).text('Temizleniyor...');

        // AJAX isteği
        $.ajax({
            url: roleCustomAjax.ajaxUrl,
            type: 'POST',
            data: {
                action: 'role_custom_clean_meta_only',
                nonce: roleCustomAjax.nonce,
                user_id: userId
            },
            success: function(response) {
                if (response.success) {
                    let message = response.data.message;

                    // Debug bilgisi ekle
                    if (response.data.deleted_keys && response.data.deleted_keys.length > 0) {
                        message += '\n\nSilinen meta veriler:\n' + response.data.deleted_keys.join('\n');
                    }

                    if (response.data.tutor_utils_used) {
                        message += '\n\n✓ Tutor Utils remove_instructor_role() kullanıldı';
                    }

                    alert(message);

                    // Sayfayı yenileme (rol değişmediği için gerekli değil ama debug için yararlı)
                    // location.reload();
                } else {
                    alert(response.data || roleCustomAjax.messages.error);
                }
            },
            error: function() {
                alert(roleCustomAjax.messages.error);
            },
            complete: function() {
                $btn.prop('disabled', false).text(originalText);
            }
        });
    });

    // Debug için console log
    console.log('Role Custom Admin JS loaded successfully');
});
