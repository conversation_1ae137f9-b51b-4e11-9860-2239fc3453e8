# Role Custom Eklentisi Değişiklik Günlüğü

## Versiyon 1.3.0 - 2025-01-11

### ✨ Yeni Özellikler

#### 🛍️ WooCommerce Ürün Yayınlama İzni Yönetimi
- **WooCommerce Yetkileri Bölümü**: <PERSON>yarlar sayfasına yeni bölüm eklendi
- **Ürün Yayınlama Kontrolü**: "Ürünleri Yayınlayabilir ve Düzenleyebilir" checkbox'ı
- **Dinamik Yetki Yönetimi**: `publish_products` yetkisi ayarlara göre eklenir/kaldırılır
- **Ürün Durumu Filtreleme**: Yeni ürünler ve düzenlemeler için pending/publish kontrolü

#### 📊 Gelişmiş Durum Raporlama
- **WooCommerce Entegrasyonu Kartı**: WooCommerce durumu ve yetkileri gösterimi
- **<PERSON>rün Yayınlama Durum Kartı**: Eğitmen ürün yayınlama durumu
- **Gelişmiş Genel Özet**: Hem kurs hem ürün durumlarını kapsayan özet

### 🔧 Teknik Değişiklikler

#### Yeni Metodlar
- `woocommerce_section_callback()`: WooCommerce ayarları bölümü açıklaması
- `instructors_can_publish_products_callback()`: Ürün yayınlama ayarı callback
- `manage_instructor_product_publishing()`: Ürün yayınlama izni yönetimi
- `filter_new_product_status()`: Yeni ürün durumu filtreleme
- `filter_product_save_status()`: Ürün kaydetme durumu filtreleme

#### Güncellenmiş Metodlar
- `sanitize_settings()`: Ürün yayınlama ayarı sanitizasyonu eklendi
- `display_current_status()`: WooCommerce durum kartları eklendi
- `on_settings_updated()`: Ürün yayınlama ayarları yeniden uygulaması

#### Yeni Hook'lar
```php
// WooCommerce ürün yayınlama hook'ları
add_action('init', [$this, 'manage_instructor_product_publishing'], 21);
add_filter('woocommerce_new_product_data', [$this, 'filter_new_product_status'], 10, 1);
add_action('save_post_product', [$this, 'filter_product_save_status'], 10, 3);
```

### 🎯 Kullanıcı Deneyimi İyileştirmeleri

#### Ürün Yayınlama Senaryoları

**Etkin Durumda (Checkbox İşaretli)**
- Eğitmenler ürün oluşturur → Doğrudan "publish" durumunda
- Eğitmenler ürün düzenler → Değişiklikler anında yayınlanır
- Admin onayı gerekmez

**Pasif Durumda (Checkbox İşaretsiz)**
- Eğitmenler ürün oluşturur → "pending" durumunda
- Eğitmenler ürün düzenler → Değişiklikler admin onayı bekler
- Admin manuel olarak onaylamalı

### 📋 Ayar Detayları

#### Role Custom Ayarları
- **Ayar Adı**: `role_custom_settings`
- **Yeni Alan**: `instructors_can_publish_products` (boolean)
- **Varsayılan**: `1` (etkin)

#### WooCommerce Entegrasyonu
- **Yetki Kontrolü**: `publish_products` capability
- **Post Status Filtreleme**: `woocommerce_new_product_data` filter
- **Kaydetme Kontrolü**: `save_post_product` action

### 🛡️ Güvenlik ve Uyumluluk

- ✅ Sadece `tutor_instructor` rolü etkilenir
- ✅ Admin kullanıcıları hiçbir zaman etkilenmez
- ✅ WooCommerce aktif değilse çalışmaz
- ✅ Mevcut ürün yetkileri korunur
- ✅ WordPress standartlarına uygun

---

## Versiyon 1.2.1 - 2025-01-11

### 🧹 Temizlik ve Optimizasyon

#### Kaldırılan Özellikler
- **Test Dosyaları Silindi**: `test-course-publishing.php` ve `test-admin-menu.php` dosyaları kaldırıldı
- **Test Bildirimleri Temizlendi**: WordPress admin panelinde görünen test sonuçları bildirimleri kaldırıldı
- **Gereksiz Admin Bildirimleri**: Fazla bilgilendirme mesajları temizlendi

#### Temizlenen Kodlar
- Test dosyası include kodları kaldırıldı
- Admin notices metodundaki gereksiz bildirimler temizlendi
- CHANGELOG ve README'den test referansları kaldırıldı

#### Sonuç
- ✅ Daha temiz ve profesyonel admin arayüzü
- ✅ Gereksiz bildirimler kaldırıldı
- ✅ Sadece gerekli özellikler korundu
- ✅ WordPress ayarlar sayfası tam fonksiyonel

---

## Versiyon 1.2.0 - 2025-01-11

### ✨ Yeni Özellikler

#### 🎛️ WordPress Ayarlar Sayfası Eklendi
- **Ayarlar Menüsü**: WordPress Admin → Ayarlar → Role Custom
- **Eğitim Ayarları Bölümü**: Tutor LMS eğitmen yetkilerini yönetme
- **Checkbox Kontrolü**: "Eğitmenler Kursları Direk Yayınlayabilir ve Düzenleyebilir" seçeneği
- **Gerçek Zamanlı Güncelleme**: Ayar değişikliklerinde otomatik senkronizasyon

#### 📊 Gelişmiş Durum Raporlama
- **Durum Kartları**: Görsel durum göstergeleri
- **Detaylı Kontrol**: Tutor LMS entegrasyonu, rol yetkileri, genel durum
- **Renkli Göstergeler**: Başarı (yeşil), uyarı (turuncu), hata (kırmızı)
- **Açıklayıcı Mesajlar**: Her durum için detaylı açıklamalar

#### 🎨 Özel CSS Tasarımı
- **admin-settings.css**: Ayarlar sayfası için özel stil dosyası
- **Responsive Tasarım**: Mobil uyumlu arayüz
- **Modern UI**: WordPress admin temasına uygun tasarım
- **Durum Kutuları**: Bilgi, uyarı, hata ve başarı kutuları

### 🔧 Teknik İyileştirmeler

#### Yeni Metodlar
- `add_settings_page()`: WordPress ayarlar sayfası ekleme
- `register_settings()`: Ayar alanlarını kaydetme
- `sanitize_settings()`: Ayar verilerini temizleme
- `settings_page_content()`: Ayarlar sayfası içeriği
- `display_current_status()`: Gelişmiş durum gösterimi
- `on_settings_updated()`: Ayar güncellendiğinde tetiklenen callback

#### Güncellenmiş Metodlar
- `manage_instructor_course_publishing()`: Ayarlara göre dinamik yönetim
- `enqueue_admin_scripts()`: Ayarlar sayfası CSS desteği

#### Yeni Hook'lar
```php
// Ayarlar sayfası hook'ları
add_action('admin_init', [$this, 'register_settings']);
add_action('admin_menu', [$this, 'add_settings_page'], 10003);
add_action('update_option_role_custom_settings', [$this, 'on_settings_updated'], 10, 2);
```

### 🎯 Kullanıcı Deneyimi İyileştirmeleri

#### Önceki Durum
- Kurs yayınlama izni sabit olarak etkin
- Ayar değiştirmek için kod düzenleme gerekiyordu
- Durum kontrolü sadece test dosyasında

#### Yeni Durum
- WordPress admin panelinden kolay ayar yönetimi
- Checkbox ile tek tıkla açma/kapama
- Gerçek zamanlı durum raporlama
- Görsel geri bildirim

### 📋 Ayar Detayları

#### Role Custom Ayarları
- **Ayar Adı**: `role_custom_settings`
- **Alan**: `instructors_can_publish_courses` (boolean)
- **Varsayılan**: `1` (etkin)

#### Etkin Durumda (Checkbox İşaretli)
- `tutor_option['instructor_can_publish_course']` = 'on'
- `tutor_instructor` rolüne `publish_tutor_courses` yetkisi eklenir
- Kurslar doğrudan "publish" durumunda oluşturulur

#### Pasif Durumda (Checkbox İşaretsiz)
- `tutor_option['instructor_can_publish_course']` = 'off'
- `tutor_instructor` rolünden `publish_tutor_courses` yetkisi kaldırılır
- Kurslar "pending" durumunda oluşturulur

### 🛡️ Güvenlik ve Uyumluluk

- ✅ Sadece `manage_options` yetkisi olan kullanıcılar ayarlara erişebilir
- ✅ Ayar verileri sanitize edilir
- ✅ WordPress Settings API kullanılır
- ✅ Nonce koruması
- ✅ Geriye dönük uyumluluk

---

## Versiyon 1.1.0 - 2025-01-11

### ✨ Yeni Özellikler

#### 🎯 Tutor LMS Kurs Yayınlama İzni Etkinleştirildi
- **Otomatik Ayar Güncellemesi**: `instructor_can_publish_course` ayarı otomatik olarak "on" yapılır
- **Yetki Ekleme**: `publish_tutor_courses` yetkisi Tutor Instructor rolüne otomatik eklenir
- **Doğrudan Yayınlama**: Artık kurslar "pending" (bekliyor) durumunda değil, doğrudan "publish" (yayınlandı) durumunda oluşturulur
- **Admin Onayı Gereksiz**: Instructor'lar admin onayı beklemeden kursları anında yayınlayabilir



### 🔧 Teknik Değişiklikler

#### Yeni Hook'lar
```php
// Kurs yayınlama izni hook'u
add_action('init', [$this, 'enable_instructor_course_publishing'], 20);
```

#### Yeni Metodlar
- `enable_instructor_course_publishing()`: Tutor LMS ayarlarını günceller ve yetkileri ekler

#### Dosya Yapısı
```
role custom/
├── role-custom.php (v1.2.0)
├── assets/css/admin-settings.css (YENİ)
├── README.md (güncellendi)
└── CHANGELOG.md (güncellendi)
```

### 📋 Etkilenen Ayarlar

#### Tutor LMS Ayarları
- `tutor_option['instructor_can_publish_course']` = 'on'

#### Tutor Instructor Rolü Yetkileri
- `publish_tutor_courses` yetkisi eklendi (zaten mevcut yetkiler korundu)

### 🎯 Kullanıcı Deneyimi İyileştirmeleri

#### Önceki Durum
1. Instructor kurs oluşturur
2. Kurs "pending" (bekliyor) durumunda kalır
3. Admin onayı gerekir
4. Admin manuel olarak kursu yayınlar

#### Yeni Durum
1. Instructor kurs oluşturur
2. Kurs otomatik olarak "publish" (yayınlandı) durumunda oluşturulur
3. Admin onayı gerekmez
4. Kurs anında yayında



### 🛡️ Güvenlik ve Uyumluluk

- ✅ Admin kullanıcıları etkilenmez
- ✅ Diğer roller etkilenmez
- ✅ Sadece `tutor_instructor` rolü için geçerli
- ✅ Tutor LMS eklentisi aktif değilse çalışmaz
- ✅ Mevcut yetkiler korunur
- ✅ WordPress standartlarına uygun

### 📝 Notlar

- Bu özellik sadece yeni oluşturulan kurslar için geçerlidir
- Mevcut "pending" durumundaki kurslar manuel olarak yayınlanmalıdır
- Ayarlar eklenti her etkinleştirildiğinde güncellenir

---

## Versiyon 1.0.0 - 2025-01-10

### 🎉 İlk Sürüm
- Tutor LMS menü kısıtlamaları
- WooCommerce tam erişim
- Role Custom admin menüsü
- Instructor yönetim sistemi
- Otomatik dashboard yönlendirmesi
