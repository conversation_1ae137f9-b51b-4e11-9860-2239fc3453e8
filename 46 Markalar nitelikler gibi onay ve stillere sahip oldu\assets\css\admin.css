/**
 * Role Custom Admin Styles
 * Admin sayfaları için CSS stilleri
 */

/* Role Custom Admin Sayfaları Genel Stiller */
.role-custom-admin-page {
    background: #f1f1f1;
    padding: 20px;
}

.role-custom-admin-page .wrap {
    background: #fff;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* Eğitmenler Tablosu Still<PERSON> */
.role-custom-instructors-table {
    margin-top: 20px;
}

.role-custom-instructors-table .wp-list-table {
    border: 1px solid #ddd;
    border-radius: 5px;
    overflow: hidden;
}

.role-custom-instructors-table .wp-list-table th {
    background: #f9f9f9;
    border-bottom: 1px solid #ddd;
    font-weight: 600;
    padding: 12px 10px;
}

.role-custom-instructors-table .wp-list-table td {
    padding: 12px 10px;
    border-bottom: 1px solid #f1f1f1;
}

.role-custom-instructors-table .wp-list-table tr:hover {
    background: #f9f9f9;
}

/* Kullanıcı Avatar Stili */
.role-custom-user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    margin-right: 10px;
    vertical-align: middle;
}

/* İstatistik Kartları */
.role-custom-stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.role-custom-stat-card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.role-custom-stat-card h3 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.role-custom-stat-card .stat-number {
    font-size: 32px;
    font-weight: bold;
    color: #0073aa;
    margin: 10px 0;
}

.role-custom-stat-card .stat-description {
    color: #666;
    font-size: 12px;
}

/* Ayarlar Sayfası Stiller */
.role-custom-settings-page .card {
    margin-top: 20px;
    padding: 20px;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.role-custom-settings-page .card h2 {
    margin-top: 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.role-custom-settings-page .form-table th {
    width: 200px;
    font-weight: 600;
}

.role-custom-settings-page .form-table td {
    padding-left: 20px;
}

/* Durum İkonları */
.role-custom-status-active {
    color: #46b450;
    font-weight: bold;
}

.role-custom-status-inactive {
    color: #dc3232;
    font-weight: bold;
}

/* Bildirim Stiller */
.role-custom-notice {
    margin: 15px 0;
    padding: 12px;
    border-left: 4px solid #0073aa;
    background: #f7f7f7;
}

.role-custom-notice.notice-success {
    border-left-color: #46b450;
}

.role-custom-notice.notice-warning {
    border-left-color: #ffb900;
}

.role-custom-notice.notice-error {
    border-left-color: #dc3232;
}

/* Responsive Tasarım */
@media (max-width: 768px) {
    .role-custom-stats-cards {
        grid-template-columns: 1fr;
    }
    
    .role-custom-instructors-table .wp-list-table {
        font-size: 14px;
    }
    
    .role-custom-instructors-table .wp-list-table th,
    .role-custom-instructors-table .wp-list-table td {
        padding: 8px 5px;
    }
}

/* Menü İkonu Stili */
.dashicons-admin-users:before {
    content: "\f110";
}

/* Sayfa Başlık Stili */
.role-custom-page-title {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.role-custom-page-title .dashicons {
    margin-right: 10px;
    color: #0073aa;
    font-size: 24px;
}

/* Tablo Aksiyon Butonları */
.role-custom-action-buttons {
    display: flex;
    gap: 5px;
}

.role-custom-action-buttons .button {
    padding: 4px 8px;
    font-size: 12px;
    height: auto;
    line-height: 1.4;
}

/* Loading Spinner */
.role-custom-loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #0073aa;
    border-radius: 50%;
    animation: role-custom-spin 1s linear infinite;
}

@keyframes role-custom-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Boş Durum Mesajları */
.role-custom-empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.role-custom-empty-state .dashicons {
    font-size: 48px;
    color: #ddd;
    margin-bottom: 20px;
}

.role-custom-empty-state h3 {
    margin: 0 0 10px 0;
    color: #333;
}

.role-custom-empty-state p {
    margin: 0;
    font-size: 14px;
}

/* Çıkar Butonu */
.role-custom-remove-btn {
    font-size: 12px;
    padding: 4px 8px;
    height: auto;
    line-height: 1.4;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.role-custom-remove-btn:hover {
    background: #a00 !important;
    border-color: #a00 !important;
    color: #fff !important;
}

/* İşlemler Kolonu */
.column-actions {
    width: 200px;
    text-align: center;
}

/* Action Buttons Container */
.role-custom-action-buttons {
    display: flex;
    gap: 3px;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
}

.role-custom-action-buttons .button {
    font-size: 10px !important;
    padding: 2px 4px !important;
    height: auto !important;
    line-height: 1.2 !important;
    min-width: auto !important;
}

/* Admin Badge */
.role-custom-admin-badge {
    display: inline-block;
    background: #dc3232;
    color: #fff;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Responsive Tasarım */
@media (max-width: 768px) {
    .role-custom-remove-btn {
        font-size: 11px;
        padding: 3px 6px;
    }
}
